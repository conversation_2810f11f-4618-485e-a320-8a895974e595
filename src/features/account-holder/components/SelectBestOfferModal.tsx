"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { formatCurrency } from "@/lib/utils";
import { Shield, FileText, CheckCircle, AlertTriangle } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { AuctionBid } from "../types/auction";

interface SelectBestOfferModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (bidId: string) => void;
  selectedBid: AuctionBid | null;
  currentPremium: number;
  loading?: boolean;
}

export function SelectBestOfferModal({
  isOpen,
  onClose,
  onConfirm,
  selectedBid,
  currentPremium,
  loading = false,
}: SelectBestOfferModalProps) {

  const [policySignatureAccepted, setPolicySignatureAccepted] = useState(false);
  const { toast } = useToast();

  const handleConfirm = () => {
    if (!policySignatureAccepted) {
      toast({
        title: "Confirmación requerida",
        description: "Debe confirmar que ha firmado el contrato para continuar",
        variant: "destructive",
      });
      return;
    }

    if (selectedBid) {
      onConfirm(selectedBid.id);
    }
  };

  const handleClose = () => {
    setPolicySignatureAccepted(false);
    onClose();
  };

  if (!selectedBid) return null;

  const savings = currentPremium - selectedBid.annualPremium;
  const savingsPercentage = ((savings / currentPremium) * 100).toFixed(1);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="w-12 h-12 bg-[#3ea050] rounded-lg flex items-center justify-center">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Confirmación de Firma</h2>
              <p className="text-sm text-gray-600 mt-1">Confirma que has firmado el contrato con el broker seleccionado</p>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Selected Offer Summary */}
          <div className="bg-[#f0f9f4] border border-[#3ea050]/20 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <Badge className="bg-[#3ea050] text-white px-3 py-1 text-sm font-medium">
                Oferta Seleccionada
              </Badge>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Shield className="h-4 w-4 text-gray-600" />
                  <span className="text-sm text-gray-600">Aseguradora</span>
                </div>
                <p className="font-semibold text-gray-900">{selectedBid.brokerCompany}</p>
              </div>
              
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-sm text-gray-600">Prima Anual</span>
                </div>
                <p className="text-2xl font-bold text-[#3ea050]">
                  {formatCurrency(selectedBid.annualPremium)}
                </p>
              </div>
            </div>
            
            <div className="mt-3">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-sm text-gray-600">Agente/Broker</span>
              </div>
              <p className="font-medium text-gray-900">{selectedBid.brokerName}</p>
            </div>
            
            {savings > 0 && (
              <div className="mt-4 bg-[#3ea050] text-white rounded-lg px-4 py-2">
                <span className="font-medium">Ahorro total: {formatCurrency(savings)} ({savingsPercentage}%)</span>
              </div>
            )}
          </div>

          {/* Important Information */}
          <div className="bg-[#fef3cd] border border-[#fbbf24] rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <AlertTriangle className="h-5 w-5 text-[#d97706]" />
              <span className="font-medium text-[#92400e]">Importante</span>
            </div>
            <ul className="space-y-2 text-sm text-[#92400e]">
              <li>• Asegúrate de haber firmado el contrato con <strong>{selectedBid.brokerName}</strong> de {selectedBid.brokerCompany}</li>
              <li>• Tendrás que subir tu nueva póliza en el siguiente paso</li>
              <li>• La confirmación es irreversible una vez completada</li>
            </ul>
          </div>

          {/* Confirmation Checkbox */}
          <div className="flex items-start space-x-3">
            <Checkbox
              id="policy-signature"
              checked={policySignatureAccepted}
              onCheckedChange={(checked) => setPolicySignatureAccepted(checked as boolean)}
              className="mt-1"
            />
            <Label htmlFor="policy-signature" className="text-sm leading-relaxed text-gray-700">
              Confirmo que he firmado el contrato de seguro con <strong>{selectedBid.brokerName}</strong> de la aseguradora <strong>{selectedBid.brokerCompany}</strong> y estoy listo para subir mi nueva póliza.
            </Label>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={loading}
              className="flex-1 border-gray-300 text-black hover:bg-gray-50 hover:text-black"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={!policySignatureAccepted || loading}
              className="flex-1 bg-[#3ea050] hover:bg-[#3ea050]/90 text-white font-medium"
            >
              {loading ? (
                "Procesando..."
              ) : (
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Continuar con Subida de Póliza
                </div>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}