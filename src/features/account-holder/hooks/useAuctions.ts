import { useQuery } from "@tanstack/react-query";
import { AssetType } from "@/types/policy";

export type AuctionStatus = "OPEN" | "CLOSED" | "SIGNED_POLICY" | "CANCELED" | "EXPIRED";

export interface AuctionListItem {
  id: string;
  identifier: string;
  status?: AuctionStatus;
  startDate?: string | null;
  endsAt: string; // ISO
  annualPremium: number;
  currency?: string;
  currentInsurer?: string | null;
  assetDisplayName: string;
  quotesReceived: number;
  assetType?: AssetType | null;
}

export interface AuctionsResponse {
  data: AuctionListItem[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export function useAuctions(params: {
  page: number;
  limit: number;
  status?: string;
  assetType?: string;
  search?: string;
}) {
  const { page, limit, status, assetType, search } = params;

  return useQuery<AuctionsResponse>(
    ["auctions", "account-holder", { page, limit, status, assetType, search }],
    async (): Promise<AuctionsResponse> => {
      const searchParams = new URLSearchParams();
      searchParams.set("page", String(page));
      searchParams.set("limit", String(limit));
      if (status) searchParams.set("status", status);
      if (assetType) searchParams.set("assetType", assetType);
      if (search) searchParams.set("search", search);

      const res = await fetch(`/api/account-holder/auctions/list?${searchParams.toString()}`);
      if (!res.ok) {
        const err = await res.json().catch(() => ({}));
        throw new Error(err.error || "Error al cargar las subastas");
      }
      return res.json();
    }
  );
}

export function useAuctionCounts() {
  return useQuery<{
    total: number;
    car: number;
    moto: number;
    open: number;
    closed: number;
    signedPolicy: number;
    canceled: number;
    expired: number;
  }>(
    ["auctions", "account-holder", "counts"],
    async () => {
      // Fetch all auctions to get accurate counts
      const [allRes, carRes, motoRes] = await Promise.all([
        fetch(`/api/account-holder/auctions/list?page=1&limit=1`),
        fetch(`/api/account-holder/auctions/list?page=1&limit=1&assetType=CAR`),
        fetch(`/api/account-holder/auctions/list?page=1&limit=1&assetType=MOTORCYCLE`)
      ]);

      if (!allRes.ok || !carRes.ok || !motoRes.ok) {
        const err = await allRes.json().catch(() => ({}));
        throw new Error(err.error || "Error al cargar los conteos de subastas");
      }

      const [allData, carData, motoData]: [AuctionsResponse, AuctionsResponse, AuctionsResponse] = await Promise.all([
        allRes.json(),
        carRes.json(),
        motoRes.json()
      ]);

      // Use server-reported total for accurate counts
      const total = allData.meta.total;
      const car = carData.meta.total;
      const moto = motoData.meta.total;

      // For open/closed and signedPolicy counts, we need to fetch all data
      const fullDataRes = await fetch(`/api/account-holder/auctions/list?page=1&limit=${total}`);
      if (!fullDataRes.ok) {
        const err = await fullDataRes.json().catch(() => ({}));
        throw new Error(err.error || "Error al cargar las subastas completas");
      }
      const fullData: AuctionsResponse = await fullDataRes.json();

      // Keep current time-based open/closed logic to avoid changing product semantics
      const open = fullData.data.filter(a => new Date(a.endsAt).getTime() > Date.now()).length;
      const signedPolicy = fullData.data.filter(a => a.status === "SIGNED_POLICY").length;
      const canceled = fullData.data.filter(a => a.status === "CANCELED").length;
      const expired = fullData.data.filter(a => a.status === "EXPIRED").length;
      const closed = total - open - signedPolicy - canceled - expired;

      return { total, car, moto, open, closed, signedPolicy, canceled, expired };
    }
  );
}
