/**
 * Shared type definitions for auction-related components
 * Consolidates duplicate interface definitions across components
 */

export interface AuctionBid {
  id: string;
  annualPremium: number;
  brokerName: string;
  brokerCompany: string;
  createdAt: string;
  hasDocument: boolean;
  bidCoverages?: any[];
  brokerPhone?: string | null;
  brokerEmail?: string | null;
  brokerIdentifier?: string | null;
  quoteDocument?: {
    id: string;
    fileName: string | null;
    fileSize: number | null;
    uploadedAt: string;
    url: string;
  } | null;
}

export interface AuctionDetails {
  id: string;
  status: string;
  annualPremium: number;
  // Add other auction properties as needed
}
