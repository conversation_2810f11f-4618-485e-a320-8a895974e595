/**
 * Standardized file validation utilities for API endpoints
 * Provides consistent file type, size, and content validation
 */

export class FileValidationError extends <PERSON><PERSON>r {
  constructor(
    message: string,
    public statusCode: number = 400,
    public code?: string
  ) {
    super(message)
    this.name = 'FileValidationError'
  }
}

/**
 * Configuration for file validation
 */
export interface FileValidationConfig {
  allowedTypes: readonly string[]
  maxSize: number // in bytes
  minSize?: number // in bytes
  allowedExtensions?: readonly string[]
}

/**
 * Default configurations for different file types
 */
export const FILE_VALIDATION_CONFIGS = {
  POLICY_DOCUMENT: {
    allowedTypes: ['application/pdf', 'image/jpeg', 'image/png'],
    maxSize: 10 * 1024 * 1024, // 10MB
    minSize: 1024, // 1KB
    allowedExtensions: ['.pdf', '.jpg', '.jpeg', '.png']
  },
  GENERAL_DOCUMENT: {
    allowedTypes: ['application/pdf', 'image/jpeg', 'image/png', 'image/webp'],
    maxSize: 15 * 1024 * 1024, // 15MB
    minSize: 1024, // 1KB
    allowedExtensions: ['.pdf', '.jpg', '.jpeg', '.png', '.webp']
  },
  IMAGE_ONLY: {
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
    maxSize: 5 * 1024 * 1024, // 5MB
    minSize: 1024, // 1KB
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.webp']
  }
} as const

/**
 * Validate file against configuration
 */
export function validateFile(
  file: File,
  config: FileValidationConfig
): void {
  if (!file) {
    throw new FileValidationError(
      'Archivo requerido',
      400,
      'FILE_REQUIRED'
    )
  }

  // Validate file type
  if (!config.allowedTypes.includes(file.type)) {
    const allowedTypesText = config.allowedTypes
      .map(type => {
        switch (type) {
          case 'application/pdf': return 'PDF'
          case 'image/jpeg': return 'JPG'
          case 'image/png': return 'PNG'
          case 'image/webp': return 'WEBP'
          default: return type
        }
      })
      .join(', ')
    
    throw new FileValidationError(
      `Tipo de archivo no permitido. Solo se permiten: ${allowedTypesText}`,
      400,
      'INVALID_FILE_TYPE'
    )
  }

  // Validate file size
  if (file.size > config.maxSize) {
    const maxSizeMB = Math.round(config.maxSize / (1024 * 1024))
    throw new FileValidationError(
      `El archivo es demasiado grande. Tamaño máximo: ${maxSizeMB}MB`,
      400,
      'FILE_TOO_LARGE'
    )
  }

  if (config.minSize && file.size < config.minSize) {
    const minSizeKB = Math.round(config.minSize / 1024)
    throw new FileValidationError(
      `El archivo es demasiado pequeño. Tamaño mínimo: ${minSizeKB}KB`,
      400,
      'FILE_TOO_SMALL'
    )
  }

  // Validate file extension if specified
  if (config.allowedExtensions) {
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
    if (!config.allowedExtensions.includes(fileExtension)) {
      const allowedExtText = config.allowedExtensions.join(', ')
      throw new FileValidationError(
        `Extensión de archivo no permitida. Solo se permiten: ${allowedExtText}`,
        400,
        'INVALID_FILE_EXTENSION'
      )
    }
  }
}

/**
 * Validate policy document file
 */
export function validatePolicyDocument(file: File): void {
  validateFile(file, FILE_VALIDATION_CONFIGS.POLICY_DOCUMENT)
}

/**
 * Validate general document file
 */
export function validateGeneralDocument(file: File): void {
  validateFile(file, FILE_VALIDATION_CONFIGS.GENERAL_DOCUMENT)
}

/**
 * Validate image file
 */
export function validateImageFile(file: File): void {
  validateFile(file, FILE_VALIDATION_CONFIGS.IMAGE_ONLY)
}

/**
 * Extract file from FormData with validation
 */
export function extractAndValidateFile(
  formData: FormData,
  fieldName: string = 'file',
  config: FileValidationConfig
): File {
  const file = formData.get(fieldName) as File
  
  if (!file) {
    throw new FileValidationError(
      `Campo '${fieldName}' requerido`,
      400,
      'FIELD_REQUIRED'
    )
  }

  validateFile(file, config)
  return file
}

/**
 * Extract and validate policy document from FormData
 */
export function extractPolicyDocument(
  formData: FormData,
  fieldName: string = 'file'
): File {
  return extractAndValidateFile(
    formData,
    fieldName,
    FILE_VALIDATION_CONFIGS.POLICY_DOCUMENT
  )
}

/**
 * Type guard for file validation errors
 */
export function isFileValidationError(error: unknown): error is FileValidationError {
  return error instanceof FileValidationError
}

/**
 * Get human-readable file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Check if file is an image
 */
export function isImageFile(file: File): boolean {
  return file.type.startsWith('image/')
}

/**
 * Check if file is a PDF
 */
export function isPdfFile(file: File): boolean {
  return file.type === 'application/pdf'
}