import { NextResponse } from 'next/server'
import { ApiAuthError, isApiAuthError } from './api-auth'
import { ProfileResolverError, isProfileResolverError } from '@/features/account-holder/utils/profile-resolver'
import { FileValidationError, isFileValidationError } from './file-validation'
import { ZodError } from 'zod'

/**
 * Standardized API response utilities
 * Provides consistent response formats and error handling
 */

/**
 * Standard success response format
 */
export interface ApiSuccessResponse<T = any> {
  success: true
  data?: T
  message?: string
  meta?: {
    page?: number
    limit?: number
    total?: number
    totalPages?: number
  }
}

/**
 * Standard error response format
 */
export interface ApiErrorResponse {
  success: false
  error: string
  code?: string
  details?: any
  timestamp?: string
}

/**
 * Create success response
 */
export function createSuccessResponse<T>(
  data?: T,
  message?: string,
  meta?: ApiSuccessResponse<T>['meta']
): NextResponse<ApiSuccessResponse<T>> {
  const response: ApiSuccessResponse<T> = {
    success: true,
    ...(data !== undefined && { data }),
    ...(message && { message }),
    ...(meta && { meta })
  }
  
  return NextResponse.json(response)
}

/**
 * Create error response
 */
export function createErrorResponse(
  error: string,
  statusCode: number = 500,
  code?: string,
  details?: any
): NextResponse<ApiErrorResponse> {
  const response: ApiErrorResponse = {
    success: false,
    error,
    ...(code && { code }),
    ...(details && { details }),
    timestamp: new Date().toISOString()
  }
  
  return NextResponse.json(response, { status: statusCode })
}

/**
 * Create paginated success response
 */
export function createPaginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number,
  message?: string
): NextResponse<ApiSuccessResponse<T[]>> {
  const totalPages = Math.ceil(total / limit)
  
  return createSuccessResponse(
    data,
    message,
    {
      page,
      limit,
      total,
      totalPages
    }
  )
}

/**
 * Handle and format errors consistently
 */
export function handleApiError(error: unknown): NextResponse<ApiErrorResponse> {
  console.error('API Error:', error)
  
  // Handle authentication errors
  if (isApiAuthError(error)) {
    return createErrorResponse(
      error.message,
      error.statusCode,
      error.code
    )
  }
  
  // Handle profile resolver errors
  if (isProfileResolverError(error)) {
    return createErrorResponse(
      error.message,
      error.statusCode,
      error.code
    )
  }
  
  // Handle file validation errors
  if (isFileValidationError(error)) {
    return createErrorResponse(
      error.message,
      error.statusCode,
      error.code
    )
  }
  
  // Handle Zod validation errors
  if (error instanceof ZodError) {
    return createErrorResponse(
      'Datos de entrada inválidos',
      400,
      'VALIDATION_ERROR',
      error.errors
    )
  }
  
  // Handle generic errors
  if (error instanceof Error) {
    return createErrorResponse(
      'Error interno del servidor',
      500,
      'INTERNAL_ERROR'
    )
  }
  
  // Handle unknown errors
  return createErrorResponse(
    'Error desconocido',
    500,
    'UNKNOWN_ERROR'
  )
}

/**
 * Common HTTP status responses
 */
export const ApiResponses = {
  // Success responses
  ok: <T>(data?: T, message?: string) => createSuccessResponse(data, message),
  created: <T>(data?: T, message?: string) => 
    NextResponse.json(createSuccessResponse(data, message).body, { status: 201 }),
  
  // Error responses
  badRequest: (message: string = 'Solicitud inválida', code?: string, details?: any) =>
    createErrorResponse(message, 400, code, details),
  
  unauthorized: (message: string = 'No autorizado', code?: string) =>
    createErrorResponse(message, 401, code),
  
  forbidden: (message: string = 'Acceso denegado', code?: string) =>
    createErrorResponse(message, 403, code),
  
  notFound: (message: string = 'Recurso no encontrado', code?: string) =>
    createErrorResponse(message, 404, code),
  
  conflict: (message: string = 'Conflicto', code?: string) =>
    createErrorResponse(message, 409, code),
  
  unprocessableEntity: (message: string = 'Entidad no procesable', code?: string, details?: any) =>
    createErrorResponse(message, 422, code, details),
  
  internalServerError: (message: string = 'Error interno del servidor', code?: string) =>
    createErrorResponse(message, 500, code),
  
  serviceUnavailable: (message: string = 'Servicio no disponible', code?: string) =>
    createErrorResponse(message, 503, code)
}

/**
 * Wrapper for API route handlers with consistent error handling
 */
export function withErrorHandling<T extends any[], R>(
  handler: (...args: T) => Promise<NextResponse<R>>
) {
  return async (...args: T): Promise<NextResponse<R | ApiErrorResponse>> => {
    try {
      return await handler(...args)
    } catch (error) {
      return handleApiError(error)
    }
  }
}

/**
 * Type guards for response types
 */
export function isSuccessResponse(response: any): response is ApiSuccessResponse {
  return response && response.success === true
}

export function isErrorResponse(response: any): response is ApiErrorResponse {
  return response && response.success === false
}