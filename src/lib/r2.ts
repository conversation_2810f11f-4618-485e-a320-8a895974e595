import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { randomUUID } from "crypto";

const R2_ACCOUNT_ID = process.env.R2_ACCOUNT_ID;
const R2_ACCESS_KEY_ID = process.env.R2_ACCESS_KEY_ID;
const R2_SECRET_ACCESS_KEY = process.env.R2_SECRET_ACCESS_KEY;
const R2_PUBLIC_URL = process.env.R2_PUBLIC_URL;

if (!R2_ACCOUNT_ID || !R2_ACCESS_KEY_ID || !R2_SECRET_ACCESS_KEY) {
  // In a real app, you'd want to log this error to a service like Sentry
  // For now, we'll throw an error to make it obvious during development
  throw new Error("Cloudflare R2 credentials are not defined in environment variables");
}

const r2Client = new S3Client({
  region: "auto",
  endpoint: `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: R2_ACCESS_KEY_ID,
    secretAccessKey: R2_SECRET_ACCESS_KEY,
  },
});

/**
 * Generates a public URL for an R2 object key
 * NOTE: This function is currently not used in the main application flow.
 * Documents are accessed via secure API route /api/documents/download instead.
 * @param key - The R2 object key (e.g., "policies/uuid-filename.pdf")
 * @returns The public URL to access the file, or a fallback API route URL
 */
export function getR2PublicUrl(key: string): string {
  // If R2_PUBLIC_URL is not configured, return the secure API route
  if (!R2_PUBLIC_URL) {
    console.warn("R2_PUBLIC_URL not configured, using secure API route instead");
    return `/api/documents/download?key=${encodeURIComponent(key)}`;
  }

  // Ensure the key doesn't start with a slash
  const cleanKey = key.startsWith('/') ? key.slice(1) : key;

  // Ensure the public URL doesn't end with a slash
  const cleanPublicUrl = R2_PUBLIC_URL.endsWith('/') ? R2_PUBLIC_URL.slice(0, -1) : R2_PUBLIC_URL;

  return `${cleanPublicUrl}/${cleanKey}`;
}

/**
 * Uploads a file to Cloudflare R2 storage
 * @param file - The file to upload
 * @param location - The folder location in the bucket (default: "policies")
 * @returns The R2 object key for the uploaded file
 */
export async function uploadToR2(
  file: File,
  location = "policies"
): Promise<string> {
  const fileBuffer = Buffer.from(await file.arrayBuffer());
  const fileId = randomUUID();
  const bucketName = process.env.R2_BUCKET_NAME!;

  const key = `${location}/${fileId}-${file.name}`;

  const command = new PutObjectCommand({
    Bucket: bucketName,
    Key: key,
    Body: fileBuffer,
    ContentType: file.type,
  });

  await r2Client.send(command);
  return key;
}

export { r2Client };