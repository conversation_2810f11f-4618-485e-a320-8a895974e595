import { createClient } from '@/lib/supabase/server'
import { NextRequest } from 'next/server'
import { User } from '@supabase/supabase-js'

/**
 * Standardized authentication utility for API routes
 * Provides consistent user authentication and role validation
 */
export class ApiAuthError extends Error {
  constructor(
    message: string,
    public statusCode: number = 401,
    public code?: string
  ) {
    super(message)
    this.name = 'ApiAuthError'
  }
}

/**
 * Get current authenticated user from request
 * Throws ApiAuthError if user is not authenticated
 */
export async function getCurrentUser(request?: NextRequest): Promise<User> {
  const supabase = await createClient()
  
  const { data: { user }, error } = await supabase.auth.getUser()
  
  if (error || !user) {
    throw new ApiAuthError('No autorizado', 401, 'UNAUTHORIZED')
  }
  
  return user
}

/**
 * Validate user has required role
 * Throws ApiAuthError if user doesn't have the required role
 */
export async function requireRole(
  user: User,
  requiredRole: string
): Promise<void> {
  const supabase = await createClient()
  
  const { data: profile } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', user.id)
    .single()
  
  if (!profile || profile.role !== requiredRole) {
    throw new ApiAuthError(
      `Acceso denegado. Se requiere rol: ${requiredRole}`,
      403,
      'INSUFFICIENT_PERMISSIONS'
    )
  }
}

/**
 * Check if user has any of the specified roles
 */
export async function hasAnyRole(
  user: User,
  roles: string[]
): Promise<boolean> {
  const supabase = await createClient()
  
  const { data: profile } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', user.id)
    .single()
  
  return profile ? roles.includes(profile.role) : false
}

/**
 * Get user role
 */
export async function getUserRole(user: User): Promise<string | null> {
  const supabase = await createClient()
  
  const { data: profile } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', user.id)
    .single()
  
  return profile?.role || null
}

/**
 * Middleware function to authenticate and optionally validate role
 */
export async function authenticateRequest(
  request?: NextRequest,
  requiredRole?: string
): Promise<{ user: User; role: string | null }> {
  const user = await getCurrentUser(request)
  const role = await getUserRole(user)
  
  if (requiredRole) {
    await requireRole(user, requiredRole)
  }
  
  return { user, role }
}

/**
 * Type guard for API auth errors
 */
export function isApiAuthError(error: unknown): error is ApiAuthError {
  return error instanceof ApiAuthError
}