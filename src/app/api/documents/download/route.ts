import { NextRequest, NextResponse } from "next/server";
import { r2Client } from "@/lib/r2";
import { db } from "@/lib/db";
import { GetObjectCommand } from "@aws-sdk/client-s3";
import { getCurrentUser } from "@/lib/api-auth";
import { handleApiError, ApiResponses } from "@/lib/api-responses";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');

    if (!key) {
      return ApiResponses.badRequest("La clave del documento es requerida");
    }

    // Authenticate the user
    const user = await getCurrentUser(request);

    // Verify that the user has access to this document
    const document = await db.documentation.findFirst({
      where: {
        url: key,
        OR: [
          {
            accountHolder: {
              userId: user.id
            }
          },
          {
            broker: {
              userId: user.id
            }
          }
        ]
      },
      include: {
        accountHolder: true,
        broker: true
      }
    });

    if (!document) {
      return NextResponse.json(
        { error: "Document not found or access denied" },
        { status: 404 }
      );
    }

    // Fetch the file directly from R2 using the AWS SDK
    const bucketName = process.env.R2_BUCKET_NAME!;
    const getObjectCommand = new GetObjectCommand({
      Bucket: bucketName,
      Key: key,
    });

    const r2Response = await r2Client.send(getObjectCommand);

    if (!r2Response.Body) {
      return ApiResponses.notFound("Document not available");
    }

    // Convert the stream to buffer
    const fileBuffer = await r2Response.Body.transformToByteArray();

    // Return the file with appropriate headers
    return new NextResponse(Buffer.from(fileBuffer), {
      status: 200,
      headers: {
        'Content-Type': document.mimeType || 'application/octet-stream',
        'Content-Disposition': `attachment; filename="${document.fileName || 'document'}"`,
        'Content-Length': fileBuffer.byteLength.toString(),
      },
    });

  } catch (error) {
    return handleApiError(error);
  }
}
