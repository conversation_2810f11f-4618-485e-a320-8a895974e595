import { NextRequest } from "next/server";
import { db } from "@/lib/db";
import { AuctionState } from "@prisma/client";
import { getCurrentUser } from "@/lib/api-auth";
import { createSuccessResponse, handleApiError, ApiResponses } from "@/lib/api-responses";
import { PolicyUploadService } from "@/lib/services/policy-upload.service";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user using centralized utility
    const user = await getCurrentUser(request);
    
    // Await params to access its properties
    const { id } = await params;

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const fileName = formData.get("fileName") as string;
    const bidId = formData.get("bidId") as string;

    // Validate required fields
    if (!bidId) {
      return ApiResponses.badRequest(
        "ID de oferta requerido",
        "BID_ID_REQUIRED"
      );
    }

    // Verify auction exists and belongs to the user
    const auction = await db.auction.findFirst({
      where: {
        id: id,
        accountHolder: {
          userId: user.id,
        },
        status: AuctionState.CLOSED,
        signatureConfirmed: true, // Must have signature confirmed first
      },
      include: {
        policy: true,
        accountHolder: true,
        bids: {
          where: {
            id: bidId,
          },
        },
      },
    });

    if (!auction) {
      return ApiResponses.notFound(
        "Subasta no encontrada, no autorizada, o firma no confirmada",
        "AUCTION_NOT_FOUND"
      );
    }

    if (auction.bids.length === 0) {
      return ApiResponses.notFound(
        "Oferta no encontrada en esta subasta",
        "BID_NOT_FOUND"
      );
    }

    // Upload file using unified service (includes AI validation)
    const uploadResult = await PolicyUploadService.uploadAuctionPolicyFile(
      file,
      fileName,
      auction.accountHolderId,
      auction.id
    );

    // Update auction with selected bid, signature confirmation, new policy document, and finalization
    await db.auction.update({
      where: {
        id: auction.id,
      },
      data: {
        selectedBidId: bidId,
        selectedAt: new Date(),
        signatureConfirmed: true,
        signatureConfirmedAt: new Date(),
        newPolicyDocumentId: uploadResult.documentation.id,
        finalizedAt: new Date(),
      },
    });

    // Update policy with new document
    await db.policy.update({
      where: {
        id: auction.policyId,
      },
      data: {
        documentId: uploadResult.documentation.id,
      },
    });

    return createSuccessResponse(
      { documentation: uploadResult.documentation },
      "Póliza subida exitosamente"
    );
  } catch (error) {
    return handleApiError(error);
  }
}