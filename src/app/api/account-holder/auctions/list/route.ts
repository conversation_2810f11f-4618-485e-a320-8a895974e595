import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/api-auth";
import { resolveAccountHolderProfile } from "@/features/account-holder/utils/profile-resolver";
import { createSuccessResponse, handleApiError, ApiResponses } from "@/lib/api-responses";
import { db } from "@/lib/db";
import { z } from "zod";
import { normalizeForSearch } from "@/lib/text-utils";
import { formatInsurerCompany } from "@/lib/format-insurer";

// Validation schema for query parameters (mirrors policies list)
const listAuctionsSchema = z.object({
  page: z.string().optional().default("1").transform(Number),
  limit: z.string().optional().default("10").transform(Number),
  status: z.string().optional(), // AuctionState filter
  assetType: z.string().optional(), // Filter via related policy.asset.assetType
  search: z.string().optional(),
});

function toIdentifier(id: string) {
  return `ZEE-AU-${id.slice(-6).toUpperCase()}`;
}

export async function GET(request: NextRequest) {
  try {
    // Authenticate user using centralized utility
    const user = await getCurrentUser(request);

    // Resolve account holder profile using centralized utility
    const accountHolderProfile = await resolveAccountHolderProfile(user);

    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    const validationResult = listAuctionsSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return ApiResponses.badRequest(
        "Parámetros de consulta inválidos",
        "VALIDATION_ERROR",
        validationResult.error.errors
      );
    }

    const { page, limit, status, assetType, search } = validationResult.data;
    const skip = (page - 1) * limit;

    // Build where clause
    const whereClause: any = {
      accountHolderId: accountHolderProfile.id,
    };

    // Build AND conditions array
    const andConditions: any[] = [];

    if (status && status !== "all") {
      // Accept multiple statuses comma-separated or single
      const statuses = status.includes(',') ? status.split(',') : [status];
      andConditions.push({ status: { in: statuses } });
    }

    if (assetType && assetType !== "all") {
      andConditions.push({
        policy: {
          asset: {
            assetType: assetType,
          },
        },
      });
    }

    if (search) {
      // Normalize search term for accent-insensitive comparison
      const normalizedSearch = normalizeForSearch(search);
      
      // Try to parse search as number for premium comparison
      const searchAsNumber = parseFloat(search);
      const isNumericSearch = !isNaN(searchAsNumber);

      const searchConditions = [
        // Search in insurer company (enum-based search with accent normalization)
        {
          policy: {
            insurerCompany: {
              in: Object.values({
                MAPFRE: "MAPFRE",
                ALLIANZ: "ALLIANZ",
                AXA: "AXA",
                GENERALI: "GENERALI",
                SANTALUCIA: "SANTALUCIA",
                MUTUA_MADRILENA: "MUTUA_MADRILENA",
                DIRECT_SEGUROS: "DIRECT_SEGUROS",
                LINEA_DIRECTA: "LINEA_DIRECTA",
                REALE_SEGUROS: "REALE_SEGUROS",
                ZURICH: "ZURICH",
                CATALANA_OCCIDENTE: "CATALANA_OCCIDENTE",
                DKV: "DKV",
                FIATC: "FIATC",
                HELVETIA: "HELVETIA",
                PLUS_ULTRA: "PLUS_ULTRA",
                AEGON: "AEGON",
                QUALITAS_AUTO: "QUALITAS_AUTO",
                BALOISE: "BALOISE",
                PELAYO: "PELAYO",
                MMT_SEGUROS: "MMT_SEGUROS",
                NATIONALE_NEDERLANDEN: "NATIONALE_NEDERLANDEN",
                LIBERTY_SEGUROS: "LIBERTY_SEGUROS",
                ADESLAS: "ADESLAS",
                ASISA: "ASISA",
                SANITAS: "SANITAS",
                CASER: "CASER",
                OCASO: "OCASO",
                ARAG: "ARAG",
                EUROP_ASSISTANCE: "EUROP_ASSISTANCE",
                INTERMUTUAS: "INTERMUTUAS",
                MGS_SEGUROS: "MGS_SEGUROS",
                SEGURCAIXA_ADESLAS: "SEGURCAIXA_ADESLAS",
                VERTI: "VERTI",
                GENESIS: "GENESIS",
                OTHER: "OTRAS"
              }).filter(company => normalizeForSearch(company).includes(normalizedSearch))
            }
          }
        },
        // Search in vehicle brand and model
        {
          policy: {
            asset: {
              vehicleDetails: {
                OR: [
                  { brand: { contains: search, mode: "insensitive" } },
                  { model: { contains: search, mode: "insensitive" } },
                ],
              },
            },
          },
        },
      ];

      // Add premium search if the search term is numeric
      if (isNumericSearch) {
        searchConditions.push(
          // Exact match
          {
            policy: {
              premium: {
                equals: searchAsNumber
              }
            }
          } as any,
          // Range match (within 1% tolerance for decimal precision)
          {
            policy: {
              premium: {
                gte: searchAsNumber * 0.99,
                lte: searchAsNumber * 1.01
              }
            }
          } as any
        );
      }

      andConditions.push({ OR: searchConditions });
    }

    // Apply all conditions
    if (andConditions.length > 0) {
      whereClause.AND = andConditions;
    }

    const [auctions, totalCount] = await Promise.all([
      db.auction.findMany({
        where: whereClause,
        include: {
          policy: {
            include: {
              asset: {
                include: { vehicleDetails: true },
              },
            },
          },
          bids: true,
        },
        orderBy: { updatedAt: "desc" },
        skip,
        take: limit,
      }),
      db.auction.count({ where: whereClause }),
    ]);

    const transformed = auctions.map((auction) => {
      const id = auction.id;
      const identifier = toIdentifier(id);
      const premium = auction.policy?.premium?.toNumber() || 0;
      const insurer = auction.policy?.insurerCompany || null;

      let assetDisplayName = "Sin información del activo";
      const asset = auction.policy?.asset;
      if (asset?.vehicleDetails) {
        const v = asset.vehicleDetails;
        const brand = v.brand || "Sin marca";
        const model = v.model || "Sin modelo";
        const year = v.year || "Sin año";
        assetDisplayName = `${brand} ${model} (${year})`;
      } else if (asset?.description) {
        assetDisplayName = asset.description;
      }

      return {
        id,
        identifier,
        status: auction.status,
        startDate: auction.startDate?.toISOString?.() || null,
        endsAt: auction.endDate?.toISOString?.() || new Date().toISOString(),
        annualPremium: premium,
        currency: "EUR",
        currentInsurer: insurer ? formatInsurerCompany(insurer) : null,
        assetDisplayName,
        assetType: asset?.assetType || null,
        quotesReceived: auction.bids.length,
      };
    });

    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return NextResponse.json({
      success: true,
      data: transformed,
      meta: {
        page,
        limit,
        total: totalCount,
        totalPages,
      },
    }, {
      headers: { "Cache-Control": "no-store" },
    });
  } catch (error) {
    return handleApiError(error);
  }
}
