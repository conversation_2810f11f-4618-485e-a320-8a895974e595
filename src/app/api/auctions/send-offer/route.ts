import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/api-auth";
import { createSuccessResponse, handleApiError, ApiResponses } from "@/lib/api-responses";

// Zod schema for request validation
const sendOfferSchema = z.object({
  policyId: z.string().uuid("Invalid policy ID format"),
  annualPremium: z.number().positive("Annual premium must be positive"),
  fileUrl: z.string().url("Invalid file URL").optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Authenticate user and ensure they are a broker
    const user = await getCurrentUser(request);
    
    if (user.role !== "BROKER") {
      return ApiResponses.forbidden("Solo los brokers pueden enviar ofertas");
    }

    // Get broker profile
    const brokerProfile = await db.brokerProfile.findUnique({
      where: { userId: user.id }
    });

    if (!brokerProfile) {
      return ApiResponses.notFound("Perfil de broker no encontrado");
    }

    const body = await request.json();
    
    // Validate request body
    const validatedData = sendOfferSchema.parse(body);
    const { policyId, annualPremium, fileUrl } = validatedData;

    // Find the auction for this policy
    const auction = await db.auction.findFirst({
      where: {
        policyId: policyId,
        status: "OPEN", // Only allow bids on open auctions
      },
    });

    if (!auction) {
      return ApiResponses.notFound("Subasta no encontrada o no activa");
    }

    // Check if broker already has a bid for this auction
    const existingBid = await db.bid.findFirst({
      where: {
        auctionId: auction.id,
        brokerId: brokerProfile.id,
      },
    });

    if (existingBid) {
      // Update existing bid
      await db.bid.update({
        where: { id: existingBid.id },
        data: {
          amount: annualPremium,
        },
      });
    } else {
      // Create new bid
      await db.bid.create({
        data: {
          auctionId: auction.id,
          brokerId: brokerProfile.id,
          amount: annualPremium,
        },
      });
    }

    return createSuccessResponse({ success: true });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return ApiResponses.badRequest(
        "Datos de solicitud inválidos",
        "VALIDATION_ERROR",
        error.errors
      );
    }
    return handleApiError(error);
  }
}