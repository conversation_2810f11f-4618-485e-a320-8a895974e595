# Development Environment Setup Guide

## Overview
This guide provides step-by-step instructions for setting up the complete Zeeguros development environment with all auction automation systems, triggers, functions, and scripts.

## Prerequisites
- Node.js 18+ installed
- Supabase CLI installed and configured
- PostgreSQL access (via Supabase)
- Environment variables configured in `.env.local`

## Required Environment Variables
Ensure your `.env.local` file contains:
```bash
# Database
DATABASE_URL="postgresql://..."
DIRECT_URL="postgresql://..."

# Supabase
NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Other required variables...
```

## Complete Environment Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Complete Database Rebuild (Recommended)
This command performs a complete environment recreation:
```bash
npm run db:rebuild
```

**What this does:**
1. Resets Prisma migrations (`migrate:reset --force`)
2. Sets up infrastructure (extensions, functions, cron jobs)
3. Applies all migrations (002, 003, 004, 005, 006)
4. Configures database settings
5. Deploys Edge Functions
6. Applies security policies
7. Seeds the database with test data

### 3. Manual Step-by-Step Setup (Alternative)

If you prefer manual control or need to troubleshoot:

#### Step 1: Reset Database
```bash
npm run migrate:reset -- --force
```

#### Step 2: Setup Infrastructure
```bash
npm run db:setup-infrastructure
```
This runs:
- `db:setup-extensions` - PostgreSQL extensions
- `db:setup-functions` - Database functions
- `db:setup-cron` - Cron job scheduling

#### Step 3: Apply Migrations
```bash
npm run db:setup-migrations
```
This applies migrations in order:
- `002_auction_expiration_cron.sql` - Auction closing automation
- `003_notification_system.sql` - Notification system
- `004_update_functions_env_vars.sql` - Environment variable security
- `005_secure_environment_variables.sql` - Security constraints
- `006_auction_winner_failsafe.sql` - Winner selection failsafe

#### Step 4: Configure Database
```bash
npm run db:setup-config
```

#### Step 5: Deploy Edge Functions
```bash
npm run db:deploy-functions
```
Deploys the `sendAuctionNotification` Edge Function.

#### Step 6: Apply Security Policies
```bash
npm run db:apply-policies
```

#### Step 7: Seed Database
```bash
npm run db:seed
```

## Auction System Components

### Automated Systems Deployed
1. **Auction Expiration Cron Job**
   - Runs every 5 minutes
   - Closes expired auctions automatically
   - Timezone-aware (Europe/Madrid)

2. **Notification System**
   - Detects newly closed auctions
   - Triggers Edge Function for winner selection
   - Sends admin and user notifications

3. **Winner Selection Edge Function**
   - Automatically selects top 3 winners
   - Uses 70% price + 30% coverage scoring
   - Saves winners to database
   - Sends winner notifications

4. **Failsafe Database Trigger**
   - Backup winner selection mechanism
   - Activates if Edge Function fails
   - Ensures winners are always created

### Database Migrations Applied
- **002**: Auction expiration cron job setup
- **003**: Comprehensive notification system
- **004**: Environment variable security for functions
- **005**: Security constraints for sensitive data
- **006**: Auction winner failsafe trigger system

## Verification Steps

### 1. Check Cron Jobs
```sql
SELECT * FROM cron.job;
```
Should show:
- `close-expired-auctions` (every 5 minutes)
- `comprehensive-auction-notifications` (every 5 minutes)

### 2. Check Database Functions
```sql
SELECT proname FROM pg_proc WHERE proname LIKE '%auction%';
```
Should include:
- `auto_select_auction_winners`
- `close_expired_auctions_manual`
- `trigger_comprehensive_auction_notifications`

### 3. Check Edge Functions
```bash
supabase functions list
```
Should show: `sendAuctionNotification`

### 4. Check Test Data
```sql
SELECT COUNT(*) FROM auction WHERE status = 'CLOSED';
SELECT COUNT(*) FROM auction_winner;
```
Should show closed auctions with corresponding winners.

## Troubleshooting

### Common Issues

1. **Migration Failures**
   - Check database permissions
   - Verify environment variables
   - Run migrations individually to isolate issues

2. **Edge Function Deployment Issues**
   - Ensure Supabase CLI is authenticated
   - Check function code for syntax errors
   - Verify environment variables in Supabase dashboard

3. **Cron Job Not Running**
   - Check pg_cron extension is enabled
   - Verify cron job scheduling syntax
   - Check database logs for errors

### Manual Commands

```bash
# Individual migration commands
npm run db:run-migration-002
npm run db:run-migration-003
npm run db:run-migration-004
npm run db:run-migration-005
npm run db:run-migration-006

# Database utilities
npm run db:studio          # Open Prisma Studio
npm run migrate:status     # Check migration status
npm run db:reset-complete  # Complete reset including auth users
```

## Development Workflow

1. **Daily Development**: Use existing database
2. **Fresh Start**: Run `npm run db:rebuild`
3. **Testing Changes**: Use `npm run db:seed` to refresh test data
4. **Production Deploy**: Ensure all migrations are applied

## Success Indicators

After successful setup, you should see:
- ✅ All migrations applied successfully
- ✅ Cron jobs scheduled and running
- ✅ Edge Function deployed and accessible
- ✅ Test auctions with winners created
- ✅ Notification system active
- ✅ Failsafe triggers installed

The auction system is now fully automated with dual-layer reliability for winner selection.
